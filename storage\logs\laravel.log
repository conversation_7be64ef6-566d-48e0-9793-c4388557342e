[2025-05-29 16:17:24] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`tasks` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `tasks` add constraint `tasks_user_id_foreign` foreign key (`user_id`) references `users` (`id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`tasks` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `tasks` add constraint `tasks_user_id_foreign` foreign key (`user_id`) references `users` (`id`)) at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `ta...', Array, Object(Closure))
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `ta...', Array, Object(Closure))
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `ta...')
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('tasks', Object(Closure))
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\database\\migrations\\2025_05_29_161024_create_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_29_1610...', Object(Closure))
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_29_1610...', Object(Closure))
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Kuliah S2\\\\Se...', 1, false)
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`tasks` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `ta...', Array)
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `ta...', Array, Object(Closure))
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `ta...', Array, Object(Closure))
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `ta...')
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('tasks', Object(Closure))
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\database\\migrations\\2025_05_29_161024_create_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_29_1610...', Object(Closure))
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_29_1610...', Object(Closure))
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Kuliah S2\\\\Se...', 1, false)
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-05-29 16:19:20] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`task_categories` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `task_categories` add constraint `task_categories_task_id_foreign` foreign key (`task_id`) references `tasks` (`id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`task_categories` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `task_categories` add constraint `task_categories_task_id_foreign` foreign key (`task_id`) references `tasks` (`id`)) at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `ta...', Array, Object(Closure))
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `ta...', Array, Object(Closure))
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `ta...')
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('task_categories', Object(Closure))
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\database\\migrations\\2025_05_29_161053_create_task_categories_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_29_1610...', Object(Closure))
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_29_1610...', Object(Closure))
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Kuliah S2\\\\Se...', 1, false)
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `uas_todo`.`task_categories` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `ta...', Array)
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `ta...', Array, Object(Closure))
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('alter table `ta...', Array, Object(Closure))
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `ta...')
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('task_categories', Object(Closure))
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\database\\migrations\\2025_05_29_161053_create_task_categories_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_29_1610...', Object(Closure))
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_29_1610...', Object(Closure))
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Kuliah S2\\\\Se...', 1, false)
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(53): Illuminate\\Console\\Command->call('migrate', Array)
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-05-29 16:24:43] local.ERROR: syntax error, unexpected token "class", expecting "," or ";" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"class\", expecting \",\" or \";\" at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\app\\Models\\User.php:12)
[stacktrace]
#0 {main}
"} 
[2025-05-29 16:24:54] local.ERROR: syntax error, unexpected token "class", expecting "," or ";" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"class\", expecting \",\" or \";\" at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\app\\Models\\User.php:12)
[stacktrace]
#0 {main}
"} 
[2025-05-29 16:49:01] local.ERROR: Vite manifest not found at: D:\Kuliah S2\Semester 2\Human Computer Interaction\Tugas\uas_todo\public\build/manifest.json {"view":{"view":"D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\resources\\views\\layouts\\app.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1247</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","tasks":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1267</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-739412481 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1267</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-739412481\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1666118714 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1666118714\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Vite manifest not found at: D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\public\\build/manifest.json at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\resources\\views\\layouts\\app.blade.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Kuliah S2\\\\Se...')
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Kuliah S2\\\\Se...', Array)
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Kuliah S2\\\\Se...', Array)
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Kuliah S2\\\\Se...', Array)
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\resources\\views\\tasks\\index.blade.php(23): Illuminate\\View\\View->render()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Kuliah S2\\\\Se...')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Kuliah S2\\\\Se...', Array)
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Kuliah S2\\\\Se...', Array)
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Kuliah S2\\\\Se...', Array)
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Kuliah S2\\\\Se...')
#62 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\public\\build/manifest.json at D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:728)
[stacktrace]
#0 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(293): Illuminate\\Foundation\\Vite->manifest('build')
#1 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\storage\\framework\\views\\791d729b5e3f2145c0ae6e03152a2725.php(15): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Kuliah S2\\\\Se...')
#3 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Kuliah S2\\\\Se...', Array)
#5 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Kuliah S2\\\\Se...', Array)
#6 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Kuliah S2\\\\Se...', Array)
#7 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\storage\\framework\\views\\0f466f2335ce906cfa00f456e362df49.php(25): Illuminate\\View\\View->render()
#10 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Kuliah S2\\\\Se...')
#11 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Kuliah S2\\\\Se...', Array)
#13 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Kuliah S2\\\\Se...', Array)
#14 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Kuliah S2\\\\Se...', Array)
#15 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 D:\\Kuliah S2\\Semester 2\\Human Computer Interaction\\Tugas\\uas_todo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\Kuliah S2\\\\Se...')
#62 {main}
"} 
