@extends('layouts.app')

@section('content')
    <h1>Edit Task</h1>

    <form action="{{ route('tasks.update', $task->task_id) }}" method="POST">
        @csrf
        @method('PUT')
        <label for="title">Title</label>
        <input type="text" name="title" id="title" value="{{ $task->title }}" required>
        <label for="description">Description</label>
        <textarea name="description" id="description">{{ $task->description }}</textarea>
        <label for="due_date">Due Date</label>
        <input type="datetime-local" name="due_date" id="due_date" value="{{ $task->due_date->format('Y-m-d\TH:i') }}" required>
        <label for="priority">Priority</label>
        <select name="priority" id="priority">
            <option value="High" {{ $task->priority == 'High' ? 'selected' : '' }}>High</option>
            <option value="Medium" {{ $task->priority == 'Medium' ? 'selected' : '' }}>Medium</option>
            <option value="Low" {{ $task->priority == 'Low' ? 'selected' : '' }}>Low</option>
        </select>
        <label for="status">Status</label>
        <select name="status" id="status">
            <option value="Todo" {{ $task->status == 'Todo' ? 'selected' : '' }}>Todo</option>
            <option value="InProgress" {{ $task->status == 'InProgress' ? 'selected' : '' }}>In Progress</option>
            <option value="Done" {{ $task->status == 'Done' ? 'selected' : '' }}>Done</option>
        </select>
        <button type="submit">Save Changes</button>
    </form>
@endsection
