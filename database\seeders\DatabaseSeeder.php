<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a default user for the todo app
        \App\Models\User::create([
            'username' => 'defaultuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }
}
