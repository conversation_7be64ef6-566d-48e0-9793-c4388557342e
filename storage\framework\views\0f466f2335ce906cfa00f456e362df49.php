<?php $__env->startSection('content'); ?>
    <div class="task-list">
        <h1>Your Tasks</h1>
        <a href="<?php echo e(route('tasks.create')); ?>">Create New Task</a>
        <ul>
            <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                    <a href="<?php echo e(route('tasks.show', $task->task_id)); ?>"><?php echo e($task->title); ?></a>
                    - Due: <?php echo e($task->due_date); ?>

                    - Priority: <?php echo e($task->priority); ?>

                    <a href="<?php echo e(route('tasks.edit', $task->task_id)); ?>">Edit</a>
                    <form action="<?php echo e(route('tasks.destroy', $task->task_id)); ?>" method="POST" style="display:inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit">Delete</button>
                    </form>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Kuliah S2\Semester 2\Human Computer Interaction\Tugas\uas_todo\resources\views/tasks/index.blade.php ENDPATH**/ ?>