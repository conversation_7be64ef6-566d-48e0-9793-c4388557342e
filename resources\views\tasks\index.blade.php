@extends('layouts.app')

@section('content')
    <div class="task-list">
        <h1>Your Tasks</h1>
        <a href="{{ route('tasks.create') }}">Create New Task</a>
        <ul>
            @foreach ($tasks as $task)
                <li>
                    <a href="{{ route('tasks.show', $task->task_id) }}">{{ $task->title }}</a>
                    - Due: {{ $task->due_date }}
                    - Priority: {{ $task->priority }}
                    <a href="{{ route('tasks.edit', $task->task_id) }}">Edit</a>
                    <form action="{{ route('tasks.destroy', $task->task_id) }}" method="POST" style="display:inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit">Delete</button>
                    </form>
                </li>
            @endforeach
        </ul>
    </div>
@endsection
