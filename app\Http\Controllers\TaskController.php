<?php

namespace App\Http\Controllers;

use App\Models\Task;
use Illuminate\Http\Request;

class TaskController extends Controller
{
    public function index()
    {
        // For now, get all tasks since we don't have authentication set up
        // In a real app, you'd want to filter by authenticated user
        $tasks = Task::all();
        return view('tasks.index', compact('tasks'));
    }

    public function show($id)
    {
    // Find the task by ID
    $task = Task::findOrFail($id);
    return view('tasks.show', compact('task'));
    }

    public function create()
    {
    return view('tasks.create');
    }

    public function store(Request $request)
    {
    // Validate the incoming request
    $request->validate([
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'due_date' => 'required|date',
        'priority' => 'required|in:High,Medium,Low',
        'status' => 'required|in:Todo,InProgress,Done',
    ]);

    // Create a new task
    Task::create([
        'user_id' => 1, // Default user ID for now since we don't have authentication
        'title' => $request->title,
        'description' => $request->description,
        'due_date' => $request->due_date,
        'priority' => $request->priority,
        'status' => $request->status,
        'creation_date' => now(),
        'last_updated' => now(),
    ]);

    return redirect()->route('tasks.index')->with('success', 'Task created successfully');
    }

    public function edit($id)
    {
    // Find the task by ID
    $task = Task::findOrFail($id);

    return view('tasks.edit', compact('task'));
    }

    public function update(Request $request, $id)
    {
    // Find the task by ID
    $task = Task::findOrFail($id);

    // Validate the incoming request
    $request->validate([
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'due_date' => 'required|date',
        'priority' => 'required|in:High,Medium,Low',
        'status' => 'required|in:Todo,InProgress,Done',
    ]);

    // Update the task
    $task->update([
        'title' => $request->title,
        'description' => $request->description,
        'due_date' => $request->due_date,
        'priority' => $request->priority,
        'status' => $request->status,
        'last_updated' => now(),
    ]);

    return redirect()->route('tasks.index')->with('success', 'Task updated successfully');
    }

    public function destroy($id)
    {
    // Find the task by ID
    $task = Task::findOrFail($id);

    // Delete the task
    $task->delete();

    return redirect()->route('tasks.index')->with('success', 'Task deleted successfully');
    }
}
